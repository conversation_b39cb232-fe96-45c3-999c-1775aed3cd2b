'use client';

import type { ReactElement, FC } from 'react';
import { useEffect, useState } from 'react';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import TabsHeader from './tabs-header';
import HostChatsTab from './tabs/host-chats-tab';
import NotificationsHostTab from '../notifications/notification-host-tab';
import { useSearchParams, usePathname, useRouter } from 'next/navigation';

const ChatPage: FC = (): ReactElement => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();
  const [activeTab, setActiveTab] = useState('chats');
  
  // Track if component has mounted to handle external navigation
  const [isMounted, setIsMounted] = useState(false);
  
  // Get active tab from URL params, default to 'chats'
  
  const handleOnTabChange = (value: string) => {
    setActiveTab(value);
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', value);
    replace(`${pathname}?${params.toString()}`);
  };

  // Effect to track component mount
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Effect to handle URL changes and update tab accordingly
  // This will trigger when navigating from external sources (like header menu)
  useEffect(() => {
    if (!isMounted) return; // Don't run on initial mount

    const currentTab = searchParams.get('tab');
    const validTabs = ['chats', 'notifications'];

    // If no tab param exists, set default to 'chats'
    if (!currentTab) {
      setActiveTab('chats');
      const params = new URLSearchParams(searchParams.toString());
      params.set('tab', 'chats');
      replace(`${pathname}?${params.toString()}`, { scroll: false });
      return;
    }

    // Validate that the tab parameter is one of the allowed values
    if (!validTabs.includes(currentTab)) {
      setActiveTab('chats');
      const params = new URLSearchParams(searchParams.toString());
      params.set('tab', 'chats');
      replace(`${pathname}?${params.toString()}`, { scroll: false });
    } else {
      // Update local state with valid tab from URL
      setActiveTab(currentTab);
    }
  }, [isMounted, searchParams.toString(), pathname, replace]);

  // Separate effect to handle initial load with tab parameters from external navigation
  useEffect(() => {
    const currentTab = searchParams.get('tab');
    const validTabs = ['chats', 'notifications'];

    // On initial load, if we have a valid tab parameter, ensure it's properly set
    if (currentTab && validTabs.includes(currentTab)) {
      // Tab is already valid, no need to change anything
      return;
    }

    // If no tab or invalid tab on initial load, set to default
    if (!currentTab || !validTabs.includes(currentTab)) {
      const params = new URLSearchParams(searchParams.toString());
      params.set('tab', 'chats');
      replace(`${pathname}?${params.toString()}`, { scroll: false });
    }
  }, []); // Only run on initial mount


  return (
    <div className="bg-white-50 h-full">
      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={handleOnTabChange} className="flex h-full flex-col">
        <TabsHeader />

        <TabsContent className="h-full flex-1 bg-transparent" value="chats">
          <HostChatsTab />
        </TabsContent>

        <TabsContent className="h-full flex-1 bg-transparent" value="notifications">
          <NotificationsHostTab />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ChatPage;
