'use client';

import type { ReactElement, FC } from 'react';
import { useEffect } from 'react';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import TabsHeader from './tabs-header';
import HostChatsTab from './tabs/host-chats-tab';
import NotificationsHostTab from '../notifications/notification-host-tab';
import { useSearchParams, usePathname, useRouter } from 'next/navigation';

const ChatPage: FC = (): ReactElement => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();

  // Get active tab from URL params, default to 'chats'
  const activeTab = searchParams.get('tab') || 'chats';

  const handleOnTabChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', value);
    replace(`${pathname}?${params.toString()}`);
  };

  // Effect to handle URL changes and update tab accordingly
  useEffect(() => {
    const currentTab = searchParams.get('tab');
    // If no tab param exists, set default to 'chats'
    if (!currentTab) {
      const params = new URLSearchParams(searchParams.toString());
      params.set('tab', 'chats');
      replace(`${pathname}?${params.toString()}`, { scroll: false });
    }
  }, [searchParams, pathname, replace]);



  return (
    <div className="bg-white-50 h-full">
      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={handleOnTabChange} className="flex h-full flex-col">
        <TabsHeader />

        <TabsContent className="h-full flex-1 bg-transparent" value="chats">
          <HostChatsTab />
        </TabsContent>

        <TabsContent className="h-full flex-1 bg-transparent" value="notifications">
          <NotificationsHostTab />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ChatPage;
