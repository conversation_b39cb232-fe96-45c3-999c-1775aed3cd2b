'use client';
import ArrowButton from '@/components/common/arrow-button';
import Breadcrumbs from '@/components/common/breadcrumbs';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { BreadCrumbItem } from '@/types/common';
import { useRouter, useSearchParams } from 'next/navigation';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import TabsHeader from '../tabs-header';
import GuestChatsTab from '../tabs/guest-chat-tab';
import NotificationsGuestTab from '../../notifications/notification-guest-tab';
import { ROUTES } from '@/constants';
import { useCallback, useState } from 'react';

const ChatView = () => {
  const t = useScopedI18n('inbox_page');
  const ct = useScopedI18n('common');
  const { replace, push } = useRouter();

  const [activeTab, setActiveTab] = useState('chats');

  const itemBreadCrumb: BreadCrumbItem[] = [{ href: ROUTES.HOME, label: ct('home') }, { label: t('inbox') }];
  const handleBackHomeClick = (): void => {
    push(ROUTES.HOME);
  };

  const searchParams = useSearchParams();

  const onTabChange = useCallback(
    (value: string) => {
      const newSearchParams = new URLSearchParams();
      setActiveTab(value);
      newSearchParams.set('tab', value);
      replace(`?${newSearchParams.toString()}`);
    },
    [replace, searchParams]
  );

  console.log('activeTab', activeTab);
  return (
    <div className="flex h-full flex-1 flex-col">
      <div className="border-b-gray/20  hidden border-b px-6 py-4 md:block">
        <div className="flex items-center justify-between">
          <Breadcrumbs items={itemBreadCrumb} />
          <ArrowButton label={ct('back_home')} onClick={handleBackHomeClick} />
        </div>
      </div>
      <div className="bg-white-50 mobile:mt-0 mt-4 flex h-full flex-1 flex-col">
        {/* Tabs */}
        <Tabs
          defaultValue={activeTab}
          value={activeTab}
          className="flex h-full flex-1 flex-col"
          onValueChange={onTabChange}>
          <TabsHeader />
          <TabsContent
            className="flex h-full flex-1 flex-col bg-transparent data-[state=inactive]:hidden"
            value="chats">
            <GuestChatsTab />
          </TabsContent>
          <TabsContent className="h-full flex-1 bg-transparent" value="notifications">
            <NotificationsGuestTab />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default ChatView;
